/* ============================================================================
   SNAP CHARTS - CSS FRAMEWORK
   ============================================================================ */

/* Chart Container Styles */
.snap-chart {
  position: relative; /* CRITICAL: This creates positioning context for tooltip */
  width: 100%;
  background: var(--bg-primary);
  border: 1.5px solid var(--border-color);
  border-radius: 14px;
  padding: 24px;
  box-sizing: border-box;
  font-family: 'Amazon Ember', Arial, sans-serif;
  transition: var(--theme-transition);
  outline: none; /* Remove default focus outline */
}

/* Production Mode: When no container wrapper, apply only sizing and font, no border or background */
.snap-chart-canvas.production-mode {
  /* Remove border, background, and top/left/right padding for production mode */
  border: none !important;
  background: none !important;
  padding-top: 0 !important;
  padding-left: 0 !important;
  padding-right: 0 !important;
  /* Keep bottom padding for scrollable charts to maintain scrollbar spacing */
  box-sizing: border-box;
  font-family: 'Amazon Ember', Arial, sans-serif;
  font-size: 12px; /* Match demo mode base font size */
  transition: var(--theme-transition);
}

/* For non-scrollable charts in production mode, remove all padding */
.snap-chart-canvas.production-mode:not(.snap-chart-scrollable .snap-chart-canvas) {
  padding-bottom: 0 !important;
}

/* For scrollable charts in production mode, remove margin-top to center vertically */
.snap-chart-scrollable .snap-chart-canvas.production-mode {
  margin-top: 0 !important;
}

/* Title Section (outside chart container) */
.snap-chart-title-section {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 16px;
}

/* Chart Header (inside chart container) */
.snap-chart-header {
  display: flex;
  flex-direction: column;
  margin-bottom: 0;
  gap: 16px;
}

/* Controls section in header */
.snap-chart-controls {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 16px;
}

.snap-chart-icon {
  width: 16px;
  height: 16px;
  color: var(--text-primary);
}

.snap-chart-title {
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 700;
  font-size: 14px;
  line-height: 1.197em;
  color: var(--text-primary);
  margin: 0;
}

.snap-chart-subtitle {
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 500;
  font-size: 10px;
  line-height: 1.197em;
  color: var(--text-primary);
  margin: 0;
  margin-top: 2px;
}



/* Chart Dropdown */
.snap-chart-dropdown {
  background: #F7F8FA;
  border-radius: 8px;
  padding: 12px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--theme-transition);
}

[data-theme="dark"] .snap-chart-dropdown {
  background: var(--bg-primary);
}

.snap-chart-dropdown:hover {
  background: var(--btn-hover);
}

/* Chart Button */
.snap-chart-button {
  background: #F7F8FA;
  border-radius: 4px;
  padding: 5px 10px;
  height: 32px;
  display: flex;
  align-items: center;
  gap: 10px;
  cursor: pointer;
  transition: var(--theme-transition);
  border: none;
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 500;
  font-size: 12px;
  color: var(--text-primary);
}

[data-theme="dark"] .snap-chart-button {
  background: var(--bg-secondary);
}

.snap-chart-button:hover {
  background: var(--btn-hover);
}

.snap-chart-button-icon {
  width: 8px;
  height: 8px;
}

/* Chart Canvas Area */
.snap-chart-canvas {
  position: relative;
  width: 100%;
  height: 290px; /* Increased from 251px to 290px to prevent font compression and match chart height expectations */
  margin-top: 24px;
  margin-bottom: 12px; /* Add margin at the bottom to ensure scrollbar is visible */
}

/* SVG Chart Styles */
.snap-chart-svg {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

/* Grid Lines */
.snap-chart-grid-line {
  stroke: rgba(96, 111, 149, 0.15);
  stroke-width: 1px;
  stroke-dasharray: 2, 2;
  fill: none;
}

.snap-chart-baseline {
  stroke: rgba(96, 111, 149, 0.4);
  stroke-width: 1.5px;
  stroke-dasharray: 4, 4;
}

[data-theme="dark"] .snap-chart-grid-line {
  stroke: rgba(180, 185, 197, 0.15);
}

[data-theme="dark"] .snap-chart-baseline {
  stroke: rgba(180, 185, 197, 0.4);
}

/* Chart Columns */
.snap-chart-column-group {
  cursor: pointer;
  transition: opacity 0.2s ease;
}

.snap-chart-column-group:hover {
  opacity: 0.8;
}

/* Remove hover indicators for charts with more than 180 columns */
.snap-chart-column-group.snap-chart-no-hover {
  cursor: default;
}

.snap-chart-column-group.snap-chart-no-hover:hover {
  opacity: 1;
}

.snap-chart-column-segment {
  transition: opacity 0.2s ease;
}

/* Column Labels */
.snap-chart-column-label {
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 600;
  font-size: 11px !important; /* Force 11px to override any inherited font sizes */
  fill: var(--text-primary);
  text-anchor: middle;
  dominant-baseline: central;
  pointer-events: none; /* Allow hover events to pass through by default */
}

/* Clickable labels should be interactive */
.snap-chart-column-label.snap-chart-clickable-label {
  pointer-events: auto;
}

.snap-chart-column-value {
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 600;
  font-size: 12.5px;
  fill: var(--text-primary);
  text-anchor: middle;
  dominant-baseline: central;
  pointer-events: none; /* Allow hover events to pass through to column hover areas */
}

.snap-chart-column-returns {
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 700; /* Bold per spec */
  font-size: 10px;  /* 2px smaller per spec */
  fill: #FF391F;
  text-anchor: middle;
  dominant-baseline: central;
  pointer-events: none; /* Allow hover events to pass through to column hover areas */
}

/* Dark mode returns value - same color for consistency */
[data-theme="dark"] .snap-chart-column-returns {
  fill: #FF391F; /* Same color for both light and dark modes */
}

/* Ensure returns values never dim due to comparison text opacity */
.snap-chart-column-returns.snap-chart-comparison-text { opacity: 1 !important; }

/* Zero returns value - gray color for zero values in both themes */
.snap-chart-column-returns.zero-returns {
  fill: #B4B9C5 !important; /* Gray color for zero returns */
  opacity: 0.8;
}

[data-theme="dark"] .snap-chart-column-returns.zero-returns {
  fill: #B4B9C5 !important; /* Gray color for zero returns in dark mode */
  opacity: 0.8;
}

/* Axis Labels */
.snap-chart-axis-label {
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 500;
  font-size: 11px;
  fill: var(--text-primary);
  text-anchor: start; /* Left labels are left-justified */
  dominant-baseline: central;
}

.snap-chart-axis-label.right {
  text-anchor: end; /* Right labels are right-justified */
}

/* Chart Colors - Updated Marketplace Color Scheme with Fill/Stroke Styling */
.snap-chart-color-1 {
  fill: #6033FF;
  fill-opacity: 0.6;
  stroke: #6033FF;
  stroke-width: 0.75px;
} /* Blue - US (United States) */

.snap-chart-color-2 {
  fill: #D450FF;
  fill-opacity: 0.6;
  stroke: #D450FF;
  stroke-width: 0.75px;
} /* Purple - UK (United Kingdom) */

.snap-chart-color-3 {
  fill: #FE40C7;
  fill-opacity: 0.6;
  stroke: #FE40C7;
  stroke-width: 0.75px;
} /* Magenta - DE (Germany) */

.snap-chart-color-4 {
  fill: #FF335E;
  fill-opacity: 0.6;
  stroke: #FF335E;
  stroke-width: 0.75px;
} /* Pink/Red - FR (France) */

.snap-chart-color-5 {
  fill: #FF5900;
  fill-opacity: 0.6;
  stroke: #FF5900;
  stroke-width: 0.75px;
} /* Orange - IT (Italy) */

.snap-chart-color-6 {
  fill: #36C7D3;
  fill-opacity: 0.6;
  stroke: #36C7D3;
  stroke-width: 0.75px;
} /* Cyan/Teal - ES (Spain) */

.snap-chart-color-7 {
  fill: #3BD52F;
  fill-opacity: 0.6;
  stroke: #3BD52F;
  stroke-width: 0.75px;
} /* Green - JP (Japan) */

/* Chart Filter Tabs - Based on sales-filter-div system */
.snap-chart-filter-tabs {
  width: 100%;
  height: 40px;
  display: flex;
  flex-direction: row;
  align-items: center;
  background: rgba(232, 235, 244, 0.5);
  border-radius: 6px;
  padding: 3px;
  gap: 3px;
  box-sizing: border-box;
}

[data-theme="dark"] .snap-chart-filter-tabs {
  background: #292E38;
}

.snap-chart-filter-tab {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 4px;
  padding: 0 8px;
  border-radius: 4px;
  cursor: pointer;
  background: transparent;
  height: 34px;
  flex: 1 1 0;
  justify-content: center;
  min-width: 0;
  position: relative;
}

.snap-chart-filter-tab.active {
  background: #fff;
  box-shadow: 0 2px 8px rgba(96, 111, 149, 0.04);
}

.snap-chart-filter-tab:not(.active):hover {
  background: rgba(96, 111, 149, 0.04);
  transition: background 0.2s ease;
}

[data-theme="dark"] .snap-chart-filter-tab.active {
  background: var(--bg-primary);
  box-shadow: none;
}

[data-theme="dark"] .snap-chart-filter-tab:not(.active):hover {
  background: rgba(255, 255, 255, 0.05);
}

.snap-chart-filter-tab.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: auto; /* Allow clicks for feedback */
}

.snap-chart-filter-tab.disabled:hover {
  background: transparent !important;
}

.snap-chart-filter-tab .tab-main {
  display: flex;
  align-items: center;
  justify-content: center;
}

.snap-chart-filter-tab .tab-label {
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 500;
  font-size: 11px;
  color: #606F95;
  display: block;
  margin-top: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

[data-theme="dark"] .snap-chart-filter-tab .tab-label {
  color: #B4B9C5;
}

@media (max-width: 768px) {
  .snap-chart-filter-tabs {
    flex-wrap: wrap;
    height: auto;
    min-height: 40px;
  }
  
  .snap-chart-filter-tab {
    flex: 1 1 calc(50% - 1.5px);
    min-width: calc(50% - 1.5px);
  }
  
  .snap-chart-filter-tab .tab-label {
    font-size: 10px;
  }
}

/* Insights Component */
.snap-chart-insights {
  display: flex;
  align-items: flex-start;
  gap: 30px; /* 30px horizontal gap between insights */
  margin-top: 16px; /* 16px gap below filter tabs */
  margin-bottom: 0; /* No bottom margin */
  padding: 0;
}

.snap-chart-insight {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 3px; /* 3px gap between label and value (18px - 15px from Figma) */
}

.snap-chart-insight-label {
  font-family: 'Amazon Ember', -apple-system, BlinkMacSystemFont, 'Segoe UI', Arial, sans-serif;
  font-weight: 500;
  font-size: 11px;
  line-height: 1.197; /* 1.197em from Figma */
  color: #606F95;
  margin: 0;
  padding: 0;
}

.snap-chart-insight-value {
  font-family: 'Amazon Ember', -apple-system, BlinkMacSystemFont, 'Segoe UI', Arial, sans-serif;
  font-weight: 700;
  font-size: 14px;
  line-height: 1.197; /* 1.197em from Figma */
  color: #000000;
  margin: 0;
  padding: 0;
}

.snap-chart-insight-value.return-rate,
.snap-chart-insight-value.units-returned {
  color: #FF391F; /* Red color for return rate and units returned as per Figma */
}

/* Add a special class for zero values in return rate and units returned */
.snap-chart-insight-value.return-rate.zero-value,
.snap-chart-insight-value.units-returned.zero-value {
  color: #000000; /* Black for zero values */
  opacity: 0.4;
}

/* Dark theme support for insights */
[data-theme="dark"] .snap-chart-insight-label {
  color: #B4B9C5; /* Lighter color for dark theme */
}

[data-theme="dark"] .snap-chart-insight-value {
  color: #ffffff; /* White for dark theme */
}

[data-theme="dark"] .snap-chart-insight-value.return-rate,
[data-theme="dark"] .snap-chart-insight-value.units-returned {
  color: #FF391F; /* Return rate and units returned stay red in dark theme */
}

/* Dark theme support for zero values in return rate and units returned */
[data-theme="dark"] .snap-chart-insight-value.return-rate.zero-value,
[data-theme="dark"] .snap-chart-insight-value.units-returned.zero-value {
  color: #ffffff; /* White for zero values in dark theme */
  opacity: 0.4;
}

/* Responsive design for insights */
@media (max-width: 768px) {
  .snap-chart-insights {
    gap: 20px; /* Reduced gap on mobile */
    flex-wrap: wrap;
  }
  
  .snap-chart-insight {
    flex: 1 1 calc(33.333% - 14px); /* 3 insights per row on mobile */
    min-width: calc(33.333% - 14px);
  }
  
  .snap-chart-insight-label {
    font-size: 11px;
  }
  
  .snap-chart-insight-value {
    font-size: 14px;
  }
}

/* Royalties Line */
.snap-chart-royalties-line {
  stroke: #ff00b7;
  stroke-width: 1px;
  fill: none;
  stroke-linecap: round;
  stroke-linejoin: round;
  will-change: stroke-dashoffset;
  pointer-events: none; /* Prevent interference with column hover areas */
}

/* Daily Sales History Royalties Line - Sharp with smooth curved joints */
.snap-chart-daily-sales .snap-chart-royalties-line {
  stroke-linecap: round; /* Rounded end caps */
  stroke-linejoin: round; /* Smooth curved joints where lines meet */
}

.snap-chart-royalties-line.animation-complete {
  will-change: auto; /* Reset after animation */
}

.snap-chart-royalties-dot {
  fill: #ff00b7;
  stroke: #FFFFFF;
  stroke-width: 2px;
  transition: all 0.2s ease;
  opacity: 0;
  pointer-events: none;
}

.snap-chart-comparison-royalties-dot {
  fill: #606F95;
  stroke: #FFFFFF;
  stroke-width: 2px;
  transition: all 0.2s ease;
  opacity: 0;
  pointer-events: none;
}

.snap-chart-column-group:hover + .snap-chart-royalties-dot,
.snap-chart-column-group.hover-active + .snap-chart-royalties-dot {
  opacity: 1;
}

@keyframes snapChartLineDraw {
  to {
    stroke-dashoffset: 0;
  }
}

@keyframes snapChartDotFadeIn {
  from {
    opacity: 0;
    transform: scale(0);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Comparison Elements (Gray) - Updated to match main column styling */
.snap-chart-comparison-segment {
  fill: #606F95;
  fill-opacity: 0.2; /* Match main column fill opacity */
  stroke: #606F95;
  stroke-opacity: 0.5;
  stroke-width: 0.75px; /* Match main column stroke weight */
}

.snap-chart-comparison-line {
  stroke: #606F95;
  stroke-width: 1px;
  fill: none;
  stroke-linecap: round;
  stroke-linejoin: round;
  opacity: 0.3;
  pointer-events: none; /* Prevent interference with column hover areas */
}

.snap-chart-comparison-dot {
  fill: #B4B9C5;
  stroke: var(--bg-primary);
  stroke-width: 2px;
  opacity: 0.6;
}

[data-theme="dark"] .snap-chart-comparison-segment {
  fill: #606F95;
  fill-opacity: 0.2; /* Match main column fill opacity */
  stroke: #606F95;
  stroke-opacity: 0.5;
  stroke-width: 0.75px; /* Match main column stroke weight */
}

[data-theme="dark"] .snap-chart-comparison-line {
  stroke: #9CA3AF;
  opacity: 0.5;
}

[data-theme="dark"] .snap-chart-comparison-dot {
  fill: #606F95;
}

/* Comparison Text (40% opacity) */
.snap-chart-comparison-text {
  opacity: 0.4;
  pointer-events: none; /* Allow hover events to pass through to column hover areas */
}

/* Selection Indicator (disabled) */
.snap-chart-selection-indicator,
.snap-chart-selection-dot {
  display: none;
}

/* Hover Tooltip */
.snap-chart-tooltip {
  position: fixed;
  background: var(--tooltip-bg);
  color: var(--tooltip-text);
  padding: 8px 12px;
  border-radius: 6px;
  font-family: 'Amazon Ember', sans-serif;
  font-size: 12px;
  font-weight: 500;
  pointer-events: none;
  z-index: 10000000; /* Highest z-index to ensure chart tooltips appear above all dashboard elements including cards */
  opacity: 0;
  transition: opacity 0.2s ease;
  white-space: nowrap;
}

.snap-chart-tooltip.visible {
  opacity: 1;
}

/* Enhanced Tooltip for Stacked Columns with Compare - Figma Design */
.snap-chart-tooltip.enhanced {
  white-space: normal;
  min-width: 190px; /* Changed from fixed width to min-width */
  max-width: 400px; /* Add max-width to prevent excessive expansion */
  padding: 16px;
  background: #000000; /* Clean black background */
  border: none; /* Remove border */
  border-radius: 12px;
  box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.25), 0 4px 6px -2px rgba(0, 0, 0, 0.1);
  font-family: 'Amazon Ember', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  color: #f9fafb;
}

/* When comparison mode is active, extend tooltip width to 2x minimum */
.snap-chart-tooltip.enhanced.comparison-mode {
  min-width: 380px; /* Changed from fixed width to min-width (double the base minimum) */
  max-width: 800px; /* Double the max-width for comparison mode */
}

.snap-chart-tooltip-header {
  font-weight: 600;
  font-size: 12px;
  color: #f9fafb;
  padding: 10px 0; /* Only 10px padding top and bottom, no margin */
  border-bottom: 1px solid rgba(51, 51, 51, 0.75); /* 75% opacity */
  text-align: left; /* Left-aligned instead of centered */
}

.snap-chart-tooltip-content {
  display: flex;
  flex-direction: column;
  gap: 0; /* Remove gap - spacing handled by individual element padding */
}

/* Two-column layout for comparison mode - exactly double width with vertical divider */
.snap-chart-tooltip-columns {
  display: grid;
  grid-template-columns: 1fr 1px 1fr; /* Left column, divider, right column */
  gap: 0;
  align-items: stretch;
}

/* Tooltip columns container - let parent tooltip handle width */
.snap-chart-tooltip-columns {
  width: 100%; /* Fill parent tooltip width */
}

.snap-chart-tooltip-column {
  display: flex;
  flex-direction: column;
  gap: 0; /* Remove gap - spacing handled by individual element padding */
  padding: 0 12px; /* Add horizontal padding for spacing from divider */
}

/* First column (Current) - no left padding */
.snap-chart-tooltip-column.main {
  padding-left: 0;
}

/* Second column (Previous) - no right padding */
.snap-chart-tooltip-column.comparison {
  padding-right: 0;
}

/* Vertical divider between columns */
.snap-chart-tooltip-divider {
  width: 1px;
  background: rgba(51, 51, 51, 0.5); /* 50% opacity */
  margin: 0;
  align-self: stretch;
}

/* Column header row with date on left and Current/Previous on right */
.snap-chart-tooltip-column-header-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0; /* Only 10px padding top and bottom, no margin */
  border-bottom: 1px solid rgba(51, 51, 51, 0.5); /* 50% opacity */
}

.snap-chart-tooltip-column-date {
  font-weight: 600;
  font-size: 12px;
  color: #f9fafb;
}

.snap-chart-tooltip-column-header {
  font-weight: 600;
  font-size: 12px;
  color: #f3f4f6;
}

.snap-chart-tooltip-column.comparison .snap-chart-tooltip-column-header {
  opacity: 0.8;
}

/* Totals section */
.snap-chart-tooltip-totals {
  display: flex;
  flex-direction: column;
  gap: 6px;
  padding: 10px 0; /* Only 10px padding top and bottom, no margin */
  border-bottom: 1px solid rgba(51, 51, 51, 0.5); /* 50% opacity */
}

.snap-chart-tooltip-total-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
}

.snap-chart-tooltip-label {
  font-size: 12px;
  color: #9ca3af;
  font-weight: 400;
}

.snap-chart-tooltip-value {
  font-weight: 600;
  font-size: 12px;
  color: #f9fafb;
}

.snap-chart-tooltip-value.returns {
  color: #FF391F;
}

/* Style global zero returns with white text and 50% opacity on black tooltip background */
.snap-chart-tooltip-value.zero-returns {
  color: #ffffff; /* White for zero returns on black tooltip background */
  opacity: 0.5;
}

/* Dark mode style for zero returns */
[data-theme="dark"] .snap-chart-tooltip-value.zero-returns {
  color: #ffffff; /* White for zero returns in dark mode */
  opacity: 0.5;
}

/* Value with percentage change container */
.snap-chart-tooltip-value-with-change {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Percentage change indicator */
.snap-chart-tooltip-percentage-change {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 11px;
  font-weight: 600;
}

.snap-chart-tooltip-percentage-change.positive {
  color: #04AE2C;
}

.snap-chart-tooltip-percentage-change.negative {
  color: #FF391F;
}

/* Returns change icon styling */
.snap-chart-tooltip-returns-icon {
  width: 12px;
  height: 12px;
  flex-shrink: 0;
}

/* Marketplace breakdown section */
.snap-chart-tooltip-breakdown {
  display: flex;
  flex-direction: column;
  gap: 6px;
  padding-top: 10px; /* Only 10px padding top to match divider spacing */
}

.snap-chart-tooltip-breakdown-items {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.snap-chart-tooltip-breakdown-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 4px 0;
  font-size: 12px;
}

.snap-chart-tooltip-marketplace {
  display: flex;
  align-items: center;
  gap: 6px;
}

.snap-chart-tooltip-marketplace-label {
  font-size: 12px;
  font-weight: 500;
  color: #f3f4f6;
}

.snap-chart-tooltip-marketplace-values {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 2px;
}

.snap-chart-tooltip-marketplace-value {
  font-weight: 600;
  font-size: 12px;
  color: #f9fafb;
}

/* Make marketplace-specific returns use the same red color as main returns */
.snap-chart-tooltip-marketplace-value .returns {
  color: #FF391F;
}

/* Style zero returns with 50% opacity and white text on black tooltip background */
.snap-chart-tooltip-marketplace-value .zero-returns {
  color: #ffffff; /* White for zero returns on black tooltip background */
  opacity: 0.5;
}

/* Dark mode style for marketplace-specific zero returns */
[data-theme="dark"] .snap-chart-tooltip-marketplace-value .zero-returns {
  color: #ffffff; /* White for zero returns in dark mode */
  opacity: 0.5;
}

.snap-chart-tooltip-marketplace-royalties {
  font-weight: 500;
  font-size: 11px;
  color: #9ca3af;
}

.snap-chart-tooltip-color-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  flex-shrink: 0;
  border: 0.75px solid transparent; /* Border color will be set via JavaScript */
  /* Background color will be set via JavaScript with 60% opacity */
}

/* Color circle for pie chart tooltips - sold colors */
.snap-chart-pie-tooltip-color-circle {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  border: 0.1px solid #6B7280; /* Light gray border */
  flex-shrink: 0;
  margin-right: 8px;
}

[data-theme="dark"] .snap-chart-pie-tooltip-color-circle {
  border-color: #6B7280; /* Slightly darker gray for dark theme */
}

/* Flag icon styling */
.snap-chart-flag-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  border-radius: 3px;
  overflow: hidden;
  background: transparent;
  flex-shrink: 0;
}

.snap-chart-flag-icon svg {
  width: 16px;
  height: 16px;
  display: block;
}

.snap-chart-flag-icon.loaded {
  background: transparent;
}

.snap-chart-flag-img {
  background: none !important;
  border-radius: 0 !important;
  box-shadow: none !important;
  padding: 0 !important;
  width: 16px;
  height: 16px;
  display: block;
}

/* No data state */
.snap-chart-tooltip-no-data {
  font-size: 12px;
  color: var(--text-secondary);
  font-style: italic;
  text-align: center;
  padding: 12px;
}

/* Responsive Design for Tooltips */
@media (max-width: 768px) {
  .snap-chart-tooltip-enhanced {
    min-width: 280px;
    max-width: 320px;
  }
  
  .snap-chart-tooltip-columns {
    grid-template-columns: 1fr;
    gap: 10px;
  }
  
  .snap-chart-tooltip-breakdown-item {
    font-size: 11px;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .snap-chart-title-section {
    margin-bottom: 12px;
  }
  
  .snap-chart {
    padding: 16px;
  }
  
  .snap-chart-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .snap-chart-controls {
    align-self: stretch;
    justify-content: flex-end;
  }
  
  .snap-chart-canvas {
    height: 250px; /* Increased from 220px to 250px to maintain proportions and prevent font compression on mobile */
  }
  
  .snap-chart-column-label,
  .snap-chart-axis-label {
    font-size: 11px;
  }
  
  .snap-chart-column-value {
    font-size: 12.5px;
  }
}

/* Animation Classes */
.snap-chart-animate-in {
  opacity: 0;
  transform: translateY(20px);
  animation: snapChartSlideUp 0.5s forwards;
}

.snap-chart-animate-in.animation-complete {
  opacity: 1;
}

@keyframes snapChartSlideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.snap-chart-column-animate {
  transform-origin: bottom;
  transform: scaleY(0);
  animation: snapChartColumnGrow 0.5s forwards;
}

.snap-chart-column-animate.animation-complete {
  transform: scaleY(1);
}

@keyframes snapChartColumnGrow {
  from {
    transform: scaleY(0);
  }
  to {
    transform: scaleY(1);
  }
}

/* Loading State */
.snap-chart.loading {
  position: relative;
}

.snap-chart.loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 14px;
  z-index: 100;
}

[data-theme="dark"] .snap-chart.loading::after {
  background: var(--bg-primary);
}

/* Dark Theme Adjustments */
[data-theme="dark"] .snap-chart-title,
[data-theme="dark"] .snap-chart-subtitle,
[data-theme="dark"] .snap-chart-button {
  color: var(--text-primary);
}

[data-theme="dark"] .snap-chart-column-label,
[data-theme="dark"] .snap-chart-column-value,
[data-theme="dark"] .snap-chart-axis-label {
  fill: var(--text-primary);
}

/* Dark theme dot stroke is white in both themes */ 

/* ============================================================================
   PIE CHART STYLES
   ============================================================================ */

/* Pie Chart Container */
.snap-chart-pie {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
}

/* Pie Chart SVG - matching Figma dimensions */
.snap-chart-pie-svg {
  width: 170.59px;
  height: 170.59px;
  max-width: 170.59px;
  max-height: 170.59px;
}

/* Pie Slice Styles - crisp SVG with proper spacing */
.snap-chart-pie-slice {
  cursor: pointer;
  transition: all 0.2s ease;
  stroke: none; /* Remove stroke to prevent flash during theme transitions */
  will-change: opacity, transform;
  /* No filter - keep crisp SVG rendering */
}

.snap-chart-pie-slice:hover {
  opacity: 0.8;
  transform-origin: center;
}

/* Pie slice colors - matching exact Figma colors */
/* Base 7 colors - Original palette */
.snap-chart-pie-slice-0 { fill: #8562FF; } /* Purple - US (matches color-1) */
.snap-chart-pie-slice-1 { fill: #439DB8; } /* Medium Blue - UK (matches color-2) */
.snap-chart-pie-slice-2 { fill: #18B2B7; } /* Teal - DE (matches color-3) */
.snap-chart-pie-slice-3 { fill: #26D1A5; } /* Light Teal - FR (matches color-4) */
.snap-chart-pie-slice-4 { fill: #4FED7D; } /* Light Green - IT (matches color-5) */
.snap-chart-pie-slice-5 { fill: #9FED5E; } /* Yellow Green - ES (matches color-6) */
.snap-chart-pie-slice-6 { fill: #F2FF65; } /* Yellow - JP (matches color-7) */

/* 30% lighter variations - 7 colors */
.snap-chart-pie-slice-7 { fill: #A088FF; } /* 30% lighter #8562FF */
.snap-chart-pie-slice-8 { fill: #6FB1C6; } /* 30% lighter #439DB8 */
.snap-chart-pie-slice-9 { fill: #4CC1C5; } /* 30% lighter #18B2B7 */
.snap-chart-pie-slice-10 { fill: #5BDDB7; } /* 30% lighter #26D1A5 */
.snap-chart-pie-slice-11 { fill: #74F193; } /* 30% lighter #4FED7D */
.snap-chart-pie-slice-12 { fill: #B3F185; } /* 30% lighter #9FED5E */
.snap-chart-pie-slice-13 { fill: #F5FF86; } /* 30% lighter #F2FF65 */

/* 70% lighter variations - 7 colors */
.snap-chart-pie-slice-14 { fill: #D4C9FF; } /* 70% lighter #8562FF */
.snap-chart-pie-slice-15 { fill: #C2D7E2; } /* 70% lighter #439DB8 */
.snap-chart-pie-slice-16 { fill: #B3E1E4; } /* 70% lighter #18B2B7 */
.snap-chart-pie-slice-17 { fill: #B8F0DC; } /* 70% lighter #26D1A5 */
.snap-chart-pie-slice-18 { fill: #C0FBC9; } /* 70% lighter #4FED7D */
.snap-chart-pie-slice-19 { fill: #D9FBC2; } /* 70% lighter #9FED5E */
.snap-chart-pie-slice-20 { fill: #FCFFC2; } /* 70% lighter #F2FF65 */

/* Deep variations - 7 colors (even darker) */
.snap-chart-pie-slice-21 { fill: #6A4AD2; } /* Deep shade of #8562FF */
.snap-chart-pie-slice-22 { fill: #2F7590; } /* Deep shade of #439DB8 */
.snap-chart-pie-slice-23 { fill: #0F7E8F; } /* Deep shade of #18B2B7 */
.snap-chart-pie-slice-24 { fill: #188570; } /* Deep shade of #26D1A5 */
.snap-chart-pie-slice-25 { fill: #3BB955; } /* Deep shade of #4FED7D */
.snap-chart-pie-slice-26 { fill: #7BB936; } /* Deep shade of #9FED5E */
.snap-chart-pie-slice-27 { fill: #D4CB3D; } /* Deep shade of #F2FF65 */

/* Bright variations - 7 colors (even lighter/more saturated) */
.snap-chart-pie-slice-28 { fill: #A588FF; } /* Bright shade of #8562FF */
.snap-chart-pie-slice-29 { fill: #57BDD8; } /* Bright shade of #439DB8 */
.snap-chart-pie-slice-30 { fill: #2DD2D7; } /* Bright shade of #18B2B7 */
.snap-chart-pie-slice-31 { fill: #3EFFC5; } /* Bright shade of #26D1A5 */
.snap-chart-pie-slice-32 { fill: #6DFF9D; } /* Bright shade of #4FED7D */
.snap-chart-pie-slice-33 { fill: #B9FF7E; } /* Bright shade of #9FED5E */
.snap-chart-pie-slice-34 { fill: #FFFF85; } /* Bright shade of #F2FF65 */

/* Pie chart borders for sold colors visibility */
.snap-chart-pie-border-outer,
.snap-chart-pie-border-inner {
  stroke: #E9EBF2;
  stroke-width: 0.5;
  fill: none;
  pointer-events: none;
}

[data-theme="dark"] .snap-chart-pie-border-outer,
[data-theme="dark"] .snap-chart-pie-border-inner {
  stroke: #404040;
}

/* Center text container */
.snap-chart-pie-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  pointer-events: none;
  z-index: 10;
}

/* Center total value - REMOVED: No longer used as per Figma design */
/* .snap-chart-pie-center-value {
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 700;
  font-size: 18px;
  line-height: 1.197;
  color: var(--text-primary);
  margin: 0;
  transition: var(--theme-transition);
} */

/* Center label - matching Figma specs */
.snap-chart-pie-center-label {
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 700; /* Changed from 500 to 700 to match Figma */
  font-size: 14px; /* Exact Figma font size */
  line-height: 1.197; /* Exact Figma line height */
  color: #606F95; /* Exact Figma color */
  margin: 0;
  transition: var(--theme-transition);
}

/* Pie slice animation - JavaScript-driven sequential animation */
.snap-chart-pie-slice-animate {
  opacity: 1;
  transform-origin: center;
  transition: none; /* Remove CSS transitions - JavaScript will handle animation */
}

/* Center text animation - JavaScript-driven */
.snap-chart-pie-center-animate {
  opacity: 0;
  transform: scale(0.8);
  transform-origin: center;
  transition: opacity 0.3s ease-out, transform 0.3s ease-out;
}

.snap-chart-pie-center-animate.visible {
  opacity: 1;
  transform: scale(1);
}

/* Pie Chart Tooltip - Enhanced for pie slices */
.snap-chart-pie-tooltip {
  position: fixed; /* Changed from absolute to fixed since it's now appended to body */
  background: #000000;
  color: #FFFFFF;
  padding: 14px;
  border-radius: 8px;
  font-family: 'Amazon Ember', sans-serif;
  font-size: 12px;
  font-weight: 500;
  pointer-events: none;
  z-index: 10000000; /* Highest z-index to ensure pie chart tooltips appear above all dashboard elements including cards */
  opacity: 0;
  transition: opacity 0.2s ease;
  white-space: nowrap;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.snap-chart-pie-tooltip.visible {
  opacity: 1;
}

.snap-chart-pie-tooltip-label {
  font-weight: 600;
  font-size: 13px;
  line-height: 16px;
  margin-bottom: 4px;
  display: flex;
  align-items: center;
}

.snap-chart-pie-tooltip-value {
  font-weight: 500;
  font-size: 12px;
  opacity: 0.9;
}

.snap-chart-pie-tooltip-percentage {
  font-weight: 600;
  font-size: 12px;
  color: #FFFFFF;
  margin-left: 8px;
}

/* New styles for sold colors tooltip alignment */
.snap-chart-pie-tooltip-value-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  min-width: fit-content;
  gap: 32px; /* Minimum 32px gap between value and percentage */
}

.snap-chart-pie-tooltip-value-left {
  font-weight: 500;
  font-size: 12px;
  opacity: 0.9;
}

.snap-chart-pie-tooltip-percentage-right {
  font-weight: 600;
  font-size: 12px;
  color: #FFFFFF;
}

/* Responsive Design for Pie Chart - maintaining Figma proportions */
@media (max-width: 768px) {
  .snap-chart-pie-svg {
    width: 150px;
    height: 150px;
    max-width: 150px;
    max-height: 150px;
  }
  
  /* .snap-chart-pie-center-value {
    font-size: 16px;
  } */
  
  .snap-chart-pie-center-label {
    font-size: 12px;
  }
  
  .snap-chart-pie-tooltip {
    padding: 10px 14px;
    font-size: 11px;
  }
  
  .snap-chart-pie-tooltip-percentage {
    font-size: 11px; /* Match mobile tooltip font size */
  }
}

/* Dark theme adjustments for pie chart */
[data-theme="dark"] .snap-chart-pie-slice {
  stroke: none; /* Remove stroke to prevent flash during theme transitions */
}

/* [data-theme="dark"] .snap-chart-pie-center-value {
  color: var(--text-primary);
} */

[data-theme="dark"] .snap-chart-pie-center-label {
  fill: #FFFFFF; /* White text in dark theme for proper visibility */
}

/* ============================================================================
   SCROLLABLE CHART STYLES - LAYERED APPROACH
   ============================================================================ */

/* Scrollable Chart Container */
.snap-chart-scrollable .snap-chart-canvas {
  position: relative;
  width: 100%;
  height: 311px; /* Increased from 291px to 311px to add more space between chart and scrollbar */
  overflow: visible; /* Changed from hidden to visible to allow scrollbar to show */
  /* Remove padding-bottom to prevent chart misalignment with grid */
}

/* Fixed Background Layer - Grid and Y-Axis Labels */
.snap-chart-background-svg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  pointer-events: none; /* Allow interactions to pass through to scrollable layer */
  /* Ensure pixel-perfect alignment */
  margin: 0;
  padding: 0;
  border: 0;
}

/* Scrollable Content Container */
.snap-chart-scrollable-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow-x: auto;
  overflow-y: hidden;
  z-index: 2;
  /* Ensure pixel-perfect alignment */
  margin: 0;
  padding: 0;
  border: 0;
  
  /* Hide scrollbar completely - Chrome/WebKit only */
  -ms-overflow-style: none; /* Internet Explorer 10+ */
  
  /* Mask to fade out columns with harsh fade zones to completely hide columns behind axis labels */
  -webkit-mask: linear-gradient(
    to right,
    transparent 0px,
    transparent 60px,
    black 80px,
    black calc(100% - 80px),
    transparent calc(100% - 60px),
    transparent 100%
  );
  mask: linear-gradient(
    to right,
    transparent 0px,
    transparent 60px,
    black 80px,
    black calc(100% - 80px),
    transparent calc(100% - 60px),
    transparent 100%
  );
}

/* Remove mask for Daily Sales History charts to prevent date handle clipping */
.snap-chart-daily-sales .snap-chart-scrollable-container {
  -webkit-mask: none;
  mask: none;
}



/* Hide WebKit scrollbar completely */
.snap-chart-scrollable-container::-webkit-scrollbar {
  display: none !important;
  width: 0 !important;
  height: 0 !important;
  background: transparent !important;
}

/* Hide scrollbar for all browsers */
.snap-chart-scrollable-container {
  scrollbar-width: none !important; /* Firefox */
  -ms-overflow-style: none !important; /* Internet Explorer 10+ */
}

/* Additional WebKit scrollbar hiding for all states */
.snap-chart-scrollable-container::-webkit-scrollbar:horizontal {
  display: none !important;
  height: 0 !important;
}

.snap-chart-scrollable-container::-webkit-scrollbar:vertical {
  display: none !important;
  width: 0 !important;
}

.snap-chart-scrollable-container::-webkit-scrollbar-track {
  display: none !important;
  background: transparent !important;
}

.snap-chart-scrollable-container::-webkit-scrollbar-thumb {
  display: none !important;
  background: transparent !important;
}

.snap-chart-scrollable-container::-webkit-scrollbar-corner {
  display: none !important;
  background: transparent !important;
}

/* Additional class for Today vs Previous Years chart to force hide native scrollbar */
.hide-native-scrollbar {
  scrollbar-width: none !important; /* Firefox */
  -ms-overflow-style: none !important; /* Internet Explorer 10+ */
}

.hide-native-scrollbar::-webkit-scrollbar {
  display: none !important;
  width: 0 !important;
  height: 0 !important;
  background: transparent !important;
}

.hide-native-scrollbar::-webkit-scrollbar:horizontal {
  display: none !important;
  height: 0 !important;
}

.hide-native-scrollbar::-webkit-scrollbar:vertical {
  display: none !important;
  width: 0 !important;
}

.hide-native-scrollbar::-webkit-scrollbar-track {
  display: none !important;
  background: transparent !important;
}

.hide-native-scrollbar::-webkit-scrollbar-thumb {
  display: none !important;
  background: transparent !important;
}

.hide-native-scrollbar::-webkit-scrollbar-corner {
  display: none !important;
  background: transparent !important;
}

/* Scrollable Content SVG - Columns and Data */
.snap-chart-content-svg {
  display: block;
  height: 100%;
  min-width: 100%; /* Ensure it's at least as wide as container */
  /* Width will be set dynamically based on column count */
  /* Ensure pixel-perfect alignment */
  margin: 0;
  padding: 0;
  border: 0;
}





/* Remove old fixed axis approach - no longer needed */
.snap-chart-fixed-axis {
  display: none;
}

/* Custom Scrollbar for Scrollable Charts */
.snap-chart-custom-scrollbar {
  position: absolute;
  bottom: -27px; /* Position 32px lower (5px - 32px = -27px) to appear well below date labels */
  left: 0;
  width: 100%;
  height: 6px;
  background: transparent; /* Hidden track */
  border-radius: 3px;
  cursor: pointer;
  transition: all 0.2s ease;
  z-index: 10000001; /* Higher than tooltips to ensure scrollbar appears above tooltips when needed */
  pointer-events: auto; /* Ensure scrollbar can receive pointer events */
}

.snap-chart-scrollbar-track {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: transparent; /* Hidden track */
  border-radius: 3px;
  pointer-events: auto; /* Ensure track can receive pointer events */
  /* Removed overflow: hidden to allow tooltip to show above track */
}

.snap-chart-scrollbar-thumb {
  position: absolute; /* Absolute positioning for proper thumb placement */
  top: 0;
  left: 0;
  height: 100%;
  background: #D1D5DB; /* Darker gray for better visibility */
  border-radius: 50px; /* Perfectly rounded */
  cursor: grab;
  transition: all 0.2s ease;
  opacity: 1; /* Full opacity to match button */
  min-width: 40px;
  border: none;
  pointer-events: auto; /* Ensure thumb can receive pointer events */
}

.snap-chart-scrollbar-thumb:hover {
  background: #9CA3AF; /* Darker on hover */
}

.snap-chart-scrollbar-thumb:active,
.snap-chart-scrollbar-thumb.dragging {
  cursor: grabbing;
  background: #9CA3AF; /* Darker on active/drag */
}

/* Dark theme adjustments */
[data-theme="dark"] .snap-chart-custom-scrollbar,
[data-theme="dark"] .snap-chart-scrollbar-track {
  background: transparent; /* Hidden track */
}

[data-theme="dark"] .snap-chart-scrollbar-thumb {
  background: #4B5563; /* Darker gray for dark theme */
}

[data-theme="dark"] .snap-chart-scrollbar-thumb:hover,
[data-theme="dark"] .snap-chart-scrollbar-thumb:active,
[data-theme="dark"] .snap-chart-scrollbar-thumb.dragging {
  background: #6B7280; /* Lighter on hover in dark theme */
}

/* Hide scrollbar when not needed */
.snap-chart-custom-scrollbar.hidden {
  display: none;
}

/* Scrollbar Tooltip - Now positioned relative to canvas to avoid opacity inheritance */
.snap-chart-scrollbar-tooltip {
  position: absolute;
  background: #000000 !important; /* Force solid black background */
  color: #FFFFFF !important; /* Force white text */
  padding: 8px 12px;
  border-radius: 6px;
  font-family: 'Amazon Ember', sans-serif;
  font-size: 12px;
  font-weight: 500;
  pointer-events: none;
  z-index: 10000000; /* Highest z-index to ensure scrollbar tooltips appear above all dashboard elements */
  opacity: 0;
  transition: opacity 0.2s ease;
  white-space: nowrap;
  /* Will be positioned dynamically by JavaScript relative to thumb position */
  display: flex;
  align-items: center;
  gap: 8px;
  /* Ensure solid appearance - force no transparency */
  backdrop-filter: none !important;
  -webkit-backdrop-filter: none !important;
  /* Force solid black with no transparency inheritance */
  background-color: #000000 !important;
  border: none !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
}

.snap-chart-scrollbar-tooltip.visible {
  opacity: 1 !important;
  background: #000000 !important; /* Ensure solid black background when visible */
  background-color: #000000 !important;
}

.snap-chart-scrollbar-tooltip-icon {
  width: 16px;
  height: 16px;
  fill: #FFFFFF !important;
  flex-shrink: 0;
}

/* Dark theme scrollbar tooltip - ensure it stays solid black */
[data-theme="dark"] .snap-chart-scrollbar-tooltip {
  background: #000000 !important;
  background-color: #000000 !important;
  color: #FFFFFF !important;
}

[data-theme="dark"] .snap-chart-scrollbar-tooltip.visible {
  background: #000000 !important;
  background-color: #000000 !important;
  color: #FFFFFF !important;
}

/* Responsive Design for Scrollable Charts */
@media (max-width: 768px) {
  .snap-chart-scrollable .snap-chart-canvas {
    height: 240px; /* Increased from 220px to 240px to maintain margin between chart and scrollbar */
  }

  .snap-chart-custom-scrollbar {
    height: 6px;
    bottom: -27px; /* Position 32px lower to appear well below date labels on mobile */
  }

  .snap-chart-custom-scrollbar:hover {
    height: 8px;
    bottom: -28px; /* Adjust position when height increases */
  }
}

/* ============================================================================
   DAILY SALES HISTORY CHART STYLES
   ============================================================================ */

/* Daily Sales History Container */
.snap-chart-daily-sales .snap-chart-canvas {
  height: 351px; /* Further increased height to ensure date range controls are fully visible */
  overflow: visible; /* Ensure content doesn't get clipped */
}

/* Canvas layer for Daily Sales History (rendered below SVG overlay) */
.snap-chart-dsh-layer {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  pointer-events: auto; /* captures hover for tooltips */
  z-index: 0; /* keep canvas behind SVG overlay (labels/slider) */
}

/* Hint the browser to optimize scrolling/hover for the canvas */
.snap-chart-dsh-layer {
  will-change: transform;
}

/* Ensure the SVG overlay stays interactive above the canvas */
.snap-chart-canvas > .snap-chart-svg {
  position: relative;
  z-index: 1;
  pointer-events: auto;
}

/* Single Column Styles */
.snap-chart-single-column {
  transition: opacity 0.2s ease;
  cursor: pointer;
}

.snap-chart-column-group:hover .snap-chart-single-column {
  opacity: 0.8;
}

/* Zero Sales Days - No visual indicator, just empty space */

/* Zero Sales Stub */
.snap-chart-zero-sales-stub {
  fill: #e0e0e0;
  opacity: 0.5;
}

[data-theme="dark"] .snap-chart-zero-sales-stub {
  fill: #606f95;
  opacity: 0.5;
}

/* Date Range Controls */
.snap-chart-date-range-controls {
  pointer-events: all;
}

.snap-chart-date-range-bg {
  fill: var(--bg-secondary);
  stroke: var(--border-color);
  stroke-width: 1px;
  opacity: 0.8;
}

[data-theme="dark"] .snap-chart-date-range-bg {
  fill: var(--bg-primary);
}

/* Figma-style Slider Track - Gray background with 10% opacity */
.snap-chart-date-slider-track {
  fill: rgba(96, 111, 149, 0.1); /* Gray with 10% opacity as per Figma */
  stroke: none;
  height: 24px; /* 24px height to match Figma */
  rx: 12; /* Perfect pill shape - 12px radius (half of 24px height) */
}

[data-theme="dark"] .snap-chart-date-slider-track {
  fill: rgba(96, 111, 149, 0.1); /* Keep same gray in dark theme */
}

/* Figma-style Selected Range - Purple with drag functionality */
.snap-chart-date-slider-range {
  fill: #8562FF; /* Purple color */
  stroke: none;
  cursor: move; /* 4-direction arrows cursor to indicate the range body is draggable */
  transition: fill 0.2s ease;
  height: 24px; /* Match track height */
  rx: 12; /* Perfect pill shape - 12px radius (half of 24px height) */
}

.snap-chart-date-slider-range:hover {
  fill: #9B7BFF; /* Slightly lighter purple on hover */
}

.snap-chart-date-slider-range:active {
  cursor: move; /* Keep 4-direction arrows cursor when actively dragging */
  fill: #7A5AFF; /* Slightly darker purple when being dragged */
}

[data-theme="dark"] .snap-chart-date-slider-range {
  fill: #8562FF; /* Same purple color in dark theme */
  cursor: move; /* 4-direction arrows cursor in dark theme */
}

[data-theme="dark"] .snap-chart-date-slider-range:hover {
  fill: #9B7BFF; /* Slightly lighter purple on hover */
}

[data-theme="dark"] .snap-chart-date-slider-range:active {
  cursor: move; /* Keep 4-direction arrows cursor when actively dragging */
  fill: #7A5AFF; /* Slightly darker purple when being dragged */
}

/* Prevent hover effects during dragging */
.snap-chart-date-slider-range.dragging:hover {
  fill: #8562FF; /* Keep original color, no hover effect during drag */
}

[data-theme="dark"] .snap-chart-date-slider-range.dragging:hover {
  fill: #8562FF; /* Keep original color, no hover effect during drag in dark theme */
}

/* Figma-style Slider Handles - White circles for scaling only */
.snap-chart-date-slider-handle {
  fill: #FFFFFF; /* White handles for both light and dark mode */
  stroke: none;
  cursor: col-resize; /* Left and right arrows with vertical line cursor for scaling functionality */
  transition: all 0.2s ease;
  /* Note: These will be created as circles */
}

.snap-chart-date-slider-handle.left-handle {
  padding-left: 6px;
}

.snap-chart-date-slider-handle.right-handle {
  padding-right: 6px;
}

.snap-chart-date-slider-handle:hover {
  fill: #F5F5F5; /* Slightly gray on hover */
}

.snap-chart-date-slider-handle.dragging {
  cursor: col-resize; /* Keep left/right arrows with vertical line cursor when dragging handles */
  fill: #E5E5E5; /* Slightly darker when dragging */
}

[data-theme="dark"] .snap-chart-date-slider-handle {
  fill: #FFFFFF; /* White in dark theme */
}

[data-theme="dark"] .snap-chart-date-slider-handle:hover {
  fill: #F5F5F5; /* Slightly gray on hover in dark mode */
}

[data-theme="dark"] .snap-chart-date-slider-handle.dragging {
  cursor: col-resize; /* Keep left/right arrows with vertical line cursor when dragging handles in dark mode */
  fill: #F5F5F5; /* Slightly gray when dragging in dark mode */
}

/* Value labels above handles - Figma style */
.snap-chart-date-range-value-label {
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 600; /* Already 600, keeping as requested */
  font-size: 10px; /* Changed from 12px to 10px */
  fill: #000000; /* Black text in light mode */
  /* text-anchor: removed to allow JavaScript to control positioning */
  dominant-baseline: central;
  /* Background styling will be added via rect elements */
  pointer-events: none; /* Disable pointer events on text to prevent text cursor */
  user-select: none; /* Prevent text selection */
  -webkit-user-select: none;
  -ms-user-select: none;
}

.snap-chart-date-range-value-bg {
  fill: #000000; /* Black background as per Figma */
  stroke: none;
  rx: 6px;
  ry: 6px;
  cursor: grab; /* Make it draggable */
  transition: all 0.2s ease;
  pointer-events: all; /* Ensure it captures pointer events */
}

.snap-chart-date-range-value-bg:hover:not(.dragging) {
  fill: #333333; /* Slightly lighter on hover, but not when dragging */
}

.snap-chart-date-range-value-bg:active,
.snap-chart-date-range-value-bg.dragging {
  cursor: grabbing;
  fill: #333333;
}

/* Prevent text cursor on the value label text */
.snap-chart-date-range-value-label {
  pointer-events: none; /* Disable pointer events on text to prevent text cursor */
  user-select: none; /* Prevent text selection */
  -webkit-user-select: none;
  -ms-user-select: none;
  /* text-anchor: intentionally not set here to allow JavaScript control */
}

[data-theme="dark"] .snap-chart-date-range-value-bg {
  fill: #FFFFFF; /* White background in dark theme */
}

[data-theme="dark"] .snap-chart-date-range-value-bg:hover:not(.dragging) {
  fill: #E5E5E5; /* Slightly darker white on hover, but not when dragging */
}

[data-theme="dark"] .snap-chart-date-range-value-bg:active,
[data-theme="dark"] .snap-chart-date-range-value-bg.dragging {
  fill: #E5E5E5; /* Slightly darker white on active/drag */
}

[data-theme="dark"] .snap-chart-date-range-value-label {
  fill: #FFFFFF; /* White text in dark mode */
}

/* Drag indicators - White bars in center of range for dragging entire range */
.snap-chart-date-slider-drag-indicator {
  fill: #FFFFFF; /* White color */
  stroke: none;
  rx: 1; /* 1px border radius */
  cursor: move; /* Show 4-direction arrows cursor to indicate draggable for moving entire range */
  transition: all 0.2s ease;
  pointer-events: none; /* No separate hover - inherit from parent slider range */
}

[data-theme="dark"] .snap-chart-date-slider-drag-indicator {
  fill: #FFFFFF; /* White color in dark theme */
  rx: 1; /* 1px border radius */
  cursor: move; /* Show 4-direction arrows cursor to indicate draggable for moving entire range */
}

/* Scale labels below slider */
.snap-chart-date-range-scale-label {
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 500;
  font-size: 11px;
  fill: var(--text-primary);
  text-anchor: start; /* Changed from middle to start for left alignment */
  dominant-baseline: central;
  opacity: 0.7;
  transition: all 0.2s ease;
}

/* Clickable year labels */
.snap-chart-date-range-scale-label.clickable-year {
  cursor: pointer;
  opacity: 0.8;
}

.snap-chart-date-range-scale-label.clickable-year:hover {
  opacity: 1;
  fill: #000000; /* Changed to black to match new design */
  font-weight: 600;
}

[data-theme="dark"] .snap-chart-date-range-scale-label.clickable-year:hover {
  fill: #ffffff; /* White for dark theme */
}

.snap-chart-date-range-label {
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 500;
  font-size: 11px;
  fill: var(--text-primary);
  dominant-baseline: central;
}

.snap-chart-date-range-label.end-date {
  text-anchor: end;
}

/* Responsive Design for Daily Sales History */
@media (max-width: 768px) {
  .snap-chart-daily-sales .snap-chart-canvas {
    height: 280px; /* Reduced height for mobile */
  }
  
  .snap-chart-date-range-label {
    font-size: 10px;
  }
  
  .snap-chart-date-range-bg {
    height: 35px; /* Slightly smaller on mobile */
  }
  
  .snap-chart-date-slider-track,
  .snap-chart-date-slider-range {
    y: 12;
    height: 24px;
    rx: 12; /* Perfect pill shape - 12px radius */
  }
}

/* No animation for Daily Sales History - performance optimization for many columns */
.snap-chart-daily-sales .snap-chart-single-column {
  /* No animations to prevent performance issues with thousands of columns */
}

/* Returns segments styling - completely sharp corners */
.snap-chart-returns-segment {
  fill: #FF391F;
  transition: opacity 0.2s ease;
}

.snap-chart-returns-segment:hover {
  opacity: 0.8;
}

.snap-chart-column-group:hover .snap-chart-returns-segment {
  opacity: 0.9;
}

/* Column Hover Areas */
.snap-chart-column-hover-area {
  fill: transparent;
  pointer-events: all;
  cursor: pointer;
  /* Hover area should only extend from top to bottom of the grid area */
  /* Height and position are set in JavaScript to match the grid boundaries */
}

/* Daily Sales History Hover Area */
.snap-chart-daily-sales-hover-area {
  cursor: crosshair;
  /* Invisible but captures mouse events for smooth tooltip interaction */
}

/* Enhanced styling for monthly labels in dense data */
.snap-chart-daily-sales .snap-chart-column-label {
  font-weight: 600; /* Slightly bolder for better readability */
}

/* ============================================================================
   DATA EDITOR COMPONENT STYLES
   ============================================================================ */

/* Data Editor Container (positioned below chart container) */
.snap-chart-data-editor {
  margin-top: 24px;
  background: var(--bg-primary);
  border: 1.5px solid var(--border-color);
  border-radius: 12px;
  overflow: hidden;
  transition: var(--theme-transition);
}

.snap-chart-data-editor.collapsed .snap-chart-data-editor-content {
  display: none;
}

/* Data Editor Header */
.snap-chart-data-editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: var(--bg-secondary);
  cursor: pointer;
  transition: var(--theme-transition);
  border-bottom: 1px solid var(--border-color);
}

.snap-chart-data-editor-header:hover {
  background: var(--btn-hover);
}

.snap-chart-data-editor.collapsed .snap-chart-data-editor-header {
  border-bottom: none;
}

.snap-chart-data-editor-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 600;
  font-size: 14px;
  color: var(--text-primary);
}

.snap-chart-data-editor-title svg {
  width: 16px;
  height: 16px;
  color: var(--text-primary);
}

.snap-chart-data-editor-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 4px;
  background: transparent;
  transition: var(--theme-transition);
}

.snap-chart-data-editor-toggle:hover {
  background: var(--btn-hover);
}

.snap-chart-data-editor-toggle svg {
  width: 16px;
  height: 16px;
  color: var(--text-primary);
  transition: transform 0.2s ease;
}

.snap-chart-data-editor.collapsed .snap-chart-data-editor-toggle svg {
  transform: rotate(-90deg);
}

/* Data Editor Content */
.snap-chart-data-editor-content {
  padding: 20px;
}

/* Data Editor Tabs */
.snap-chart-data-editor-tabs {
  display: flex;
  gap: 2px;
  margin-bottom: 16px;
  background: var(--bg-secondary);
  border-radius: 8px;
  padding: 4px;
}

.snap-chart-data-editor-tab {
  flex: 1;
  padding: 8px 16px;
  background: transparent;
  border: none;
  border-radius: 6px;
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 500;
  font-size: 12px;
  color: var(--text-primary);
  cursor: pointer;
  transition: var(--theme-transition);
  opacity: 0.7;
}

.snap-chart-data-editor-tab:hover {
  opacity: 1;
  background: var(--btn-hover);
}

.snap-chart-data-editor-tab.active {
  background: var(--bg-primary);
  opacity: 1;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

[data-theme="dark"] .snap-chart-data-editor-tab.active {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

/* JSON Editor */
.snap-chart-data-editor-json {
  display: none;
  margin-bottom: 16px;
}

.snap-chart-data-editor-json.active {
  display: block;
}

.snap-chart-data-editor-textarea {
  width: 100%;
  min-height: 200px;
  max-height: 400px;
  padding: 16px;
  background: var(--bg-secondary);
  border: 1.5px solid var(--border-color);
  border-radius: 8px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.5;
  color: var(--text-primary);
  resize: vertical;
  box-sizing: border-box;
  transition: var(--theme-transition);
}

.snap-chart-data-editor-textarea:focus {
  outline: none;
  border-color: #8562FF;
  box-shadow: 0 0 0 3px rgba(133, 98, 255, 0.1);
}

.snap-chart-data-editor-textarea::placeholder {
  color: var(--text-secondary);
  opacity: 0.7;
}

/* Table Editor */
.snap-chart-data-editor-table {
  display: none;
  margin-bottom: 16px;
  max-height: 400px;
  overflow-y: auto;
  border: 1.5px solid var(--border-color);
  border-radius: 8px;
}

.snap-chart-data-editor-table.active {
  display: block;
}

.snap-chart-data-table {
  width: 100%;
  border-collapse: collapse;
  font-family: 'Amazon Ember', sans-serif;
  font-size: 12px;
  background: var(--bg-primary);
}

.snap-chart-data-table th,
.snap-chart-data-table td {
  padding: 8px 12px;
  text-align: left;
  border-bottom: 1px solid var(--border-color);
}

.snap-chart-data-table th {
  background: var(--bg-secondary);
  font-weight: 600;
  color: var(--text-primary);
  position: sticky;
  top: 0;
  z-index: 1;
}

.snap-chart-data-table td {
  background: var(--bg-primary);
}

.snap-chart-data-table input {
  width: 100%;
  padding: 4px 8px;
  background: transparent;
  border: 1px solid transparent;
  border-radius: 4px;
  font-family: 'Amazon Ember', sans-serif;
  font-size: 12px;
  color: var(--text-primary);
  box-sizing: border-box;
  transition: var(--theme-transition);
}

.snap-chart-data-table input:focus {
  outline: none;
  border-color: #8562FF;
  background: var(--bg-secondary);
}

.snap-chart-data-table-row-actions {
  width: 40px;
  text-align: center;
}

.snap-chart-data-table-row-btn {
  padding: 4px;
  background: transparent;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: var(--theme-transition);
  display: flex;
  align-items: center;
  justify-content: center;
}

.snap-chart-data-table-row-btn:hover {
  background: var(--btn-hover);
}

.snap-chart-data-table-row-btn.danger:hover {
  background: #fee2e2;
  color: #dc2626;
}

[data-theme="dark"] .snap-chart-data-table-row-btn.danger:hover {
  background: rgba(220, 38, 38, 0.1);
  color: #f87171;
}

.snap-chart-data-table-row-btn svg {
  width: 16px;
  height: 16px;
  color: var(--text-primary);
}

.snap-chart-data-table-row-btn.danger svg {
  color: #dc2626;
}

[data-theme="dark"] .snap-chart-data-table-row-btn.danger svg {
  color: #f87171;
}

/* Action Buttons */
.snap-chart-data-editor-actions {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.snap-chart-data-editor-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: var(--bg-secondary);
  border: 1.5px solid var(--border-color);
  border-radius: 6px;
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 500;
  font-size: 12px;
  color: var(--text-primary);
  cursor: pointer;
  transition: var(--theme-transition);
  text-decoration: none;
}

.snap-chart-data-editor-btn:hover {
  background: var(--btn-hover);
  border-color: var(--border-hover);
}

.snap-chart-data-editor-btn.primary {
  background: #8562FF;
  border-color: #8562FF;
  color: #FFFFFF;
}

.snap-chart-data-editor-btn.primary:hover {
  background: #7c3aed;
  border-color: #7c3aed;
}

.snap-chart-data-editor-btn svg {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
}

.snap-chart-data-editor-btn.primary svg {
  color: #FFFFFF;
}

/* Status Messages */
.snap-chart-data-editor-status {
  padding: 12px 16px;
  border-radius: 8px;
  font-family: 'Amazon Ember', sans-serif;
  font-size: 12px;
  font-weight: 500;
  margin-bottom: 16px;
  border: 1px solid transparent;
}

.snap-chart-data-editor-status.success {
  background: #dcfce7;
  color: #166534;
  border-color: #bbf7d0;
}

.snap-chart-data-editor-status.error {
  background: #fee2e2;
  color: #dc2626;
  border-color: #fecaca;
}

.snap-chart-data-editor-status.warning {
  background: #fef3c7;
  color: #d97706;
  border-color: #fde68a;
}

.snap-chart-data-editor-status.info {
  background: #dbeafe;
  color: #1d4ed8;
  border-color: #bfdbfe;
}

[data-theme="dark"] .snap-chart-data-editor-status.success {
  background: rgba(34, 197, 94, 0.1);
  color: #4ade80;
  border-color: rgba(34, 197, 94, 0.2);
}

[data-theme="dark"] .snap-chart-data-editor-status.error {
  background: rgba(239, 68, 68, 0.1);
  color: #f87171;
  border-color: rgba(239, 68, 68, 0.2);
}

[data-theme="dark"] .snap-chart-data-editor-status.warning {
  background: rgba(245, 158, 11, 0.1);
  color: #fbbf24;
  border-color: rgba(245, 158, 11, 0.2);
}

[data-theme="dark"] .snap-chart-data-editor-status.info {
  background: rgba(59, 130, 246, 0.1);
  color: #60a5fa;
  border-color: rgba(59, 130, 246, 0.2);
}

/* Helper Section */
.snap-chart-data-editor-helper {
  background: var(--bg-secondary);
  border: 1.5px solid var(--border-color);
  border-radius: 8px;
  padding: 16px;
  margin-top: 16px;
}

.snap-chart-data-editor-helper-title {
  margin: 0 0 8px 0;
  font-family: 'Amazon Ember', sans-serif;
  font-weight: 600;
  font-size: 12px;
  color: var(--text-primary);
}

.snap-chart-data-editor-helper-content {
  margin: 0;
  padding: 12px;
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 11px;
  line-height: 1.4;
  color: var(--text-primary);
  white-space: pre-wrap;
  overflow-x: auto;
  max-height: 200px;
  overflow-y: auto;
}

/* Responsive Design for Data Editor */
@media (max-width: 768px) {
  .snap-chart-data-editor-content {
    padding: 16px;
  }
  
  .snap-chart-data-editor-actions {
    flex-direction: column;
  }
  
  .snap-chart-data-editor-btn {
    justify-content: center;
  }
  
  .snap-chart-data-editor-textarea {
    min-height: 150px;
  }
  
  .snap-chart-data-table {
    font-size: 11px;
  }
  
  .snap-chart-data-table th,
  .snap-chart-data-table td {
    padding: 6px 8px;
  }
}

/* Dark Theme Adjustments for Data Editor */
[data-theme="dark"] .snap-chart-data-editor-header {
  border-bottom-color: var(--border-color);
}

[data-theme="dark"] .snap-chart-data-table th {
  background: var(--bg-secondary);
}

[data-theme="dark"] .snap-chart-data-table td {
  background: var(--bg-primary);
}

[data-theme="dark"] .snap-chart-data-table th,
[data-theme="dark"] .snap-chart-data-table td {
  border-bottom-color: var(--border-color);
}




