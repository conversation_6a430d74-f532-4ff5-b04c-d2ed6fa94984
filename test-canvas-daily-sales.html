<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Canvas Daily Sales Chart Test</title>
    <link rel="stylesheet" href="components/charts/snap-charts.css">
    <style>
        body {
            font-family: 'Amazon Ember', sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .test-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
        
        .test-description {
            font-size: 14px;
            color: #666;
            margin-bottom: 15px;
        }
        
        .controls {
            margin-bottom: 20px;
        }
        
        .controls button {
            margin-right: 10px;
            padding: 8px 16px;
            border: 1px solid #ddd;
            background: white;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .controls button:hover {
            background: #f0f0f0;
        }
        
        .controls button.active {
            background: #007bff;
            color: white;
            border-color: #007bff;
        }
        
        .chart-container {
            width: 100%;
            height: 400px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        
        .performance-info {
            margin-top: 10px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 4px;
            font-size: 12px;
            color: #666;
        }
    </style>
</head>
<body>
    <h1>Canvas Daily Sales Chart Performance Test</h1>
    
    <div class="test-container">
        <div class="test-title">Small Dataset Test (50 data points)</div>
        <div class="test-description">Testing Canvas vs SVG rendering with a small dataset</div>
        <div class="controls">
            <button onclick="renderSmallDataset('svg')" id="small-svg-btn">SVG Rendering</button>
            <button onclick="renderSmallDataset('canvas')" id="small-canvas-btn" class="active">Canvas Rendering</button>
        </div>
        <div id="small-chart" class="chart-container"></div>
        <div id="small-performance" class="performance-info"></div>
    </div>
    
    <div class="test-container">
        <div class="test-title">Medium Dataset Test (500 data points)</div>
        <div class="test-description">Testing Canvas performance with medium-sized dataset</div>
        <div class="controls">
            <button onclick="renderMediumDataset('svg')" id="medium-svg-btn">SVG Rendering</button>
            <button onclick="renderMediumDataset('canvas')" id="medium-canvas-btn" class="active">Canvas Rendering</button>
        </div>
        <div id="medium-chart" class="chart-container"></div>
        <div id="medium-performance" class="performance-info"></div>
    </div>
    
    <div class="test-container">
        <div class="test-title">Large Dataset Test (2000 data points)</div>
        <div class="test-description">Testing Canvas performance with large dataset (~2000 points)</div>
        <div class="controls">
            <button onclick="renderLargeDataset('canvas')" id="large-canvas-btn" class="active">Canvas Rendering</button>
            <button onclick="renderLargeDataset('svg')" id="large-svg-btn">SVG Rendering (Not Recommended)</button>
        </div>
        <div id="large-chart" class="chart-container"></div>
        <div id="large-performance" class="performance-info"></div>
    </div>

    <script src="components/charts/snap-charts.js"></script>
    <script>
        // Generate test data
        function generateDailySalesData(days) {
            const data = [];
            const startDate = new Date();
            startDate.setDate(startDate.getDate() - days);
            
            for (let i = 0; i < days; i++) {
                const date = new Date(startDate);
                date.setDate(startDate.getDate() + i);
                
                const sales = Math.floor(Math.random() * 100) + 10;
                const royalties = Math.floor(sales * (Math.random() * 0.3 + 0.1));
                const returns = Math.floor(Math.random() * 5);
                
                data.push({
                    dateObj: date,
                    month: date.toLocaleDateString('en-US', { month: 'short' }).toUpperCase(),
                    day: date.getDate().toString().padStart(2, '0'),
                    year: date.getFullYear().toString().slice(-2),
                    sales: sales,
                    royalties: royalties,
                    returns: returns,
                    fullDate: date.toISOString().split('T')[0]
                });
            }
            
            return data;
        }
        
        // Test functions
        function renderSmallDataset(renderType) {
            updateButtonStates('small', renderType);
            const data = generateDailySalesData(50);
            renderChart('small-chart', data, renderType, 'small-performance');
        }
        
        function renderMediumDataset(renderType) {
            updateButtonStates('medium', renderType);
            const data = generateDailySalesData(500);
            renderChart('medium-chart', data, renderType, 'medium-performance');
        }
        
        function renderLargeDataset(renderType) {
            updateButtonStates('large', renderType);
            const data = generateDailySalesData(2000);
            renderChart('large-chart', data, renderType, 'large-performance');
        }
        
        function updateButtonStates(size, renderType) {
            // Remove active class from all buttons in this group
            document.querySelectorAll(`#${size}-svg-btn, #${size}-canvas-btn`).forEach(btn => {
                btn.classList.remove('active');
            });
            
            // Add active class to selected button
            document.getElementById(`${size}-${renderType}-btn`).classList.add('active');
        }
        
        function renderChart(containerId, data, renderType, performanceId) {
            const startTime = performance.now();
            
            const chart = new SnapChart({
                container: `#${containerId}`,
                type: 'daily-sales-history',
                data: data,
                options: {
                    title: `Daily Sales History (${data.length} data points)`,
                    subtitle: `Rendered using ${renderType.toUpperCase()}`,
                    forceCanvas: renderType === 'canvas',
                    allTimeData: data
                },
                demoOptions: {
                    showContainer: false,
                    showTitle: false,
                    showDataEditor: false,
                    showControls: false,
                    showInsights: false
                }
            });
            
            const endTime = performance.now();
            const renderTime = endTime - startTime;
            
            // Update performance info
            const performanceElement = document.getElementById(performanceId);
            performanceElement.innerHTML = `
                <strong>Performance:</strong> 
                Render time: ${renderTime.toFixed(2)}ms | 
                Data points: ${data.length} | 
                Rendering: ${renderType.toUpperCase()} | 
                ${renderType === 'canvas' ? 'Canvas optimizations active' : 'SVG rendering'}
            `;
        }
        
        // Initialize with default tests
        document.addEventListener('DOMContentLoaded', function() {
            renderSmallDataset('canvas');
            renderMediumDataset('canvas');
            renderLargeDataset('canvas');
        });
    </script>
</body>
</html>
